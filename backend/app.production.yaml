# Google App Engine configuration for backend API - PRODUCTION
runtime: nodejs18
service: backend

# Instance configuration for production
automatic_scaling:
  min_instances: 1
  max_instances: 20
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6

# Environment variables for production
env_variables:
  NODE_ENV: "production"
  PORT: "8080"
  SOLANA_NETWORK: "mainnet-beta"
  SOLANA_RPC_URL: "https://api.mainnet-beta.solana.com"
  CORS_ORIGIN: "https://YOUR_PROJECT_ID.uc.r.appspot.com"
  LOG_LEVEL: "info"
  ENABLE_REQUEST_LOGGING: "true"
  ENABLE_DEBUG_ROUTES: "false"
  # Secrets are accessed via Secret Manager in the application code
  MONGODB_URI: "projects/YOUR_PROJECT_ID/secrets/mongodb-uri/versions/latest"
  JWT_SECRET: "projects/YOUR_PROJECT_ID/secrets/jwt-secret/versions/latest"
  RESEND_API_KEY: "projects/YOUR_PROJECT_ID/secrets/resend-api-key/versions/latest"
  PLATFORM_FEE_RECIPIENT_PRIVATE_KEY: "projects/YOUR_PROJECT_ID/secrets/platform-fee-private-key/versions/latest"

# Health checks
readiness_check:
  path: "/api/health"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: "/api/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Network settings
network:
  forwarded_ports:
    - 8080

# Security settings
handlers:
  - url: /api/.*
    script: auto
    secure: always
    
  - url: /.*
    script: auto
    secure: always
