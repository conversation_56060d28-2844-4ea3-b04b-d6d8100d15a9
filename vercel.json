{"version": 2, "name": "securecontract-pro", "buildCommand": "npm run vercel-build", "outputDirectory": "dist", "installCommand": "npm install", "functions": {"api/index.js": {"runtime": "nodejs18.x", "maxDuration": 30}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/index.js"}, {"source": "/sitemap.xml", "destination": "/sitemap.xml"}, {"source": "/robots.txt", "destination": "/robots.txt"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"NODE_ENV": "production"}}