# Google Cloud Environment Variables Template
# Copy this file to .env.gcloud and fill in your actual values
# DO NOT COMMIT .env.gcloud TO VERSION CONTROL

# Google Cloud Project Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_REGION=us-central1
GOOGLE_CLOUD_ZONE=us-central1-a

# Application Environment
NODE_ENV=production
PORT=8080

# Database Configuration (MongoDB Atlas recommended)
MONGODB_URI=mongodb+srv://username:<EMAIL>/digital_contracts?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
SESSION_SECRET=your-session-secret-change-this-in-production

# Email Service (Resend)
RESEND_API_KEY=re_your_resend_api_key_here

# Solana Configuration
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]

# Frontend URLs (will be updated after deployment)
VITE_API_URL=https://backend-dot-your-project-id.uc.r.appspot.com
VITE_BACKEND_URL=https://backend-dot-your-project-id.uc.r.appspot.com
VITE_FRONTEND_URL=https://your-project-id.uc.r.appspot.com
VITE_SOLANA_CLUSTER=devnet

# CORS Configuration
CORS_ORIGIN=https://your-project-id.uc.r.appspot.com

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Redis Configuration (Optional - Google Cloud Memorystore)
REDIS_URL=redis://your-redis-instance:6379

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Error Tracking (Optional)
SENTRY_DSN=https://your-sentry-dsn-here

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
ENABLE_METRICS=true

# Development Flags (set to false in production)
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false
ENABLE_DEV_TOOLS=false
