name: Deploy to Google Cloud

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GAR_LOCATION: us-central1
  SERVICE: securecontract-backend
  REGION: us-central1

jobs:
  test:
    runs-on: ubuntu-latest
    name: Test Application
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci

    - name: Run frontend tests
      run: npm run lint

    - name: Run backend tests
      run: |
        cd backend
        npm test || echo "No tests configured yet"

  deploy-staging:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: test
    runs-on: ubuntu-latest
    name: Deploy to Staging
    environment: staging
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Google Auth
      id: auth
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GCP_SA_KEY }}'

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v2'

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci

    - name: Build frontend
      run: |
        export NODE_ENV=production
        export VITE_API_URL=https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com
        export VITE_BACKEND_URL=https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com
        export VITE_FRONTEND_URL=https://${{ env.PROJECT_ID }}.uc.r.appspot.com
        export VITE_SOLANA_CLUSTER=devnet
        npm run build:frontend

    - name: Update configuration files
      run: |
        sed -i "s/YOUR_PROJECT_ID/${{ env.PROJECT_ID }}/g" app.yaml
        sed -i "s/YOUR_PROJECT_ID/${{ env.PROJECT_ID }}/g" backend/app.yaml
        sed -i "s/YOUR_PROJECT_ID/${{ env.PROJECT_ID }}/g" cloudbuild.yaml

    - name: Deploy backend to App Engine
      run: |
        gcloud app deploy backend/app.yaml \
          --version=staging-${{ github.sha }} \
          --no-promote \
          --quiet

    - name: Deploy frontend to App Engine
      run: |
        gcloud app deploy app.yaml \
          --version=staging-${{ github.sha }} \
          --promote \
          --quiet

    - name: Test deployment
      run: |
        sleep 30
        curl -f https://${{ env.PROJECT_ID }}.uc.r.appspot.com || exit 1
        curl -f https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com/api/health || exit 1

    - name: Comment PR with deployment info
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `🚀 **Staging Deployment Complete!**
            
            **Frontend:** https://${{ env.PROJECT_ID }}.uc.r.appspot.com
            **Backend:** https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com
            **Health Check:** https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com/api/health
            
            **Version:** staging-${{ github.sha }}
            **Commit:** ${{ github.sha }}`
          })

  deploy-production:
    if: github.event.inputs.environment == 'production' || (github.ref == 'refs/heads/main' && contains(github.event.head_commit.message, '[deploy-prod]'))
    needs: test
    runs-on: ubuntu-latest
    name: Deploy to Production
    environment: production
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Google Auth
      id: auth
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GCP_SA_KEY }}'

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v2'

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci

    - name: Build frontend for production
      run: |
        export NODE_ENV=production
        export VITE_API_URL=https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com
        export VITE_BACKEND_URL=https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com
        export VITE_FRONTEND_URL=https://${{ env.PROJECT_ID }}.uc.r.appspot.com
        export VITE_SOLANA_CLUSTER=mainnet-beta
        npm run build:frontend

    - name: Update configuration files for production
      run: |
        sed -i "s/YOUR_PROJECT_ID/${{ env.PROJECT_ID }}/g" app.yaml
        sed -i "s/YOUR_PROJECT_ID/${{ env.PROJECT_ID }}/g" backend/app.yaml
        sed -i "s/devnet/mainnet-beta/g" backend/app.yaml

    - name: Deploy backend to App Engine
      run: |
        gcloud app deploy backend/app.yaml \
          --version=prod-${{ github.sha }} \
          --no-promote \
          --quiet

    - name: Deploy frontend to App Engine
      run: |
        gcloud app deploy app.yaml \
          --version=prod-${{ github.sha }} \
          --promote \
          --quiet

    - name: Test production deployment
      run: |
        sleep 30
        curl -f https://${{ env.PROJECT_ID }}.uc.r.appspot.com || exit 1
        curl -f https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com/api/health || exit 1

    - name: Create GitHub Release
      if: success()
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Production Release v${{ github.run_number }}
        body: |
          🚀 **Production Deployment**
          
          **Deployed:** ${{ github.sha }}
          **Frontend:** https://${{ env.PROJECT_ID }}.uc.r.appspot.com
          **Backend:** https://backend-dot-${{ env.PROJECT_ID }}.uc.r.appspot.com
          
          **Changes in this release:**
          ${{ github.event.head_commit.message }}
        draft: false
        prerelease: false
